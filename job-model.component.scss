/*
* Model heading
*/
.model-close-button {
    background: transparent;
    border: none;
}
.model-title{
    color: white;
}
/*
* Date filter
*/
.date-filter-field{
    border-top-left-radius: 5px !important;
    border-bottom-left-radius: 5px !important;
    background: transparent !important;
}

input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-calendar-picker-indicator {
    background: transparent;
    bottom: 0;
    color: transparent;
    cursor: pointer;
    height: auto;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: auto;
}
input[type="date"]::-webkit-clear-button {
    display: none;
}
.date-filter-field-shadow {
    position: absolute;
    width: 100%;
    height: 46px;
    color: transparent;
    border: none;
    background: transparent;
    z-index: 1;
}