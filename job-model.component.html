<div class="modal-header bg-primary text-light">
  <div class="col-9">
    <h6 class="mt-2 ms-3 model-title">Add a New Job</h6>
  </div>
  <div class="col-3 text-end">
    <button type="button" class="close custom-modal-close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
      <span class="pr-10" aria-hidden="true">&times;</span>
    </button>
  </div>
</div>
<form [formGroup]="jobForm" (ngSubmit)="save()">
  <div class="modal-body popup-body-sec">
    <div class="tab-pane fade show active" id="steep" role="tabpanel" aria-labelledby="steep-tab">
      <div class="row">
        <div class="col-6 mb-3">
          <label for="Address Search">Address Search</label>
          <!-- <input type="text" class="form-control" placeholder="Search Address" #googleAddress /> -->
           <input type="text"
         class="form-control"
         placeholder="Search Address"
         #googleAddress />           
          <!-- <app-google-address-validator (manuallyEnteredAddress)="setManuallyEnteredAddress($event)"
                                          (setAddress)="getAddress($event)" [inputClass]="'form-control form-control-height'"
                                          [inputId]="'searchWord'" [inputMaxLength]="250"                                          
                                        ></app-google-address-validator> -->
          <!-- <input type="text" #addressInput [(ngModel)]="searchWord" (keyup)="onStreetNameKeyup(addressInput)" class="form-control"
            [matAutocomplete]="auto" [ngModelOptions]="{standalone: true}" />
            <mat-autocomplete #auto="matAutocomplete" (optionSelected)="onOptionSelected($event.option.value)">
              @for (result of searchResults; track result) {
                <mat-option [value]="result">
                  {{ result?.billing_Street }}, {{result?.billing_City}} {{result?.billing_State}} {{result?.billing_Postal_Code}} {{result?.billing_Country}}
                </mat-option>
              }
              @if (searchResults.length === 0) {
                <mat-option disabled>
                  No records found
                </mat-option>
              }
            </mat-autocomplete> -->
          </div>
        </div>
        <div class="row">
          @for (field of jobFormFilds; track field.field_name) {
            <div class="col-6 mb-3">
              @if (field.type == 'text') {
                <div>
                  <label [for]="field.field_name">{{field.label}} @if (field.required) {
                    <span style="color: red;">*</span>
                  }</label>
                  <input type="text"
                    class="form-control"
                    [id]="field.field_name"
                    [formControlName]="field.field_name"
                    [placeholder]="field.label"/>
                  </div>
                }
                @if (field.type == 'date') {
                  <div>
                    <label [for]="field.field_name">{{field.label}} @if (field.required) {
                      <span style="color: red;">*</span>
                    }</label>
                    <div class="input-group">
                      <input type="date"
                        class="date-filter-field-shadow"
                        [id]="field.field_name"
                        [formControlName]="field.field_name"
                        [placeholder]="field.label">
                        <input type="text"
                          class="form-control date-filter-field"
                          [value]="jobForm?.get(field.field_name).value| date:'MM-dd-yyyy'"
                          [placeholder]="field.label" readonly>
                          <span class="input-group-text" style="padding: 1px 12px;">
                            <i class="fa fa-calendar-days"></i>
                          </span>
                        </div>
                      </div>
                    }
                    @if (field.type == 'select') {
                      <div>
                        <label [for]="field.field_name">{{field.label}} @if (field.required) {
                          <span style="color: red;">*</span>
                        }</label>
                        <select class="form-select form-select-sm"
                          aria-label=".form-select-sm example"
                          [formControlName]="field.field_name">
                          <option value="">Select {{field.label}}</option>
                          @for (type of field.options; track type.value) {
                            <option  [value]="type.value">{{type.text}}</option>
                          }
                        </select>
                      </div>
                    }
                    @if (jobForm?.get(field.field_name)?.invalid && (jobForm?.get(field.field_name)?.touched || submitted)) {
                      <div class="text-danger validation-message">
                        @for (validation of field.validations; track validation.name) {
                          <div>
                            @if (jobForm.controls[field.field_name].errors?.[validation.name]) {
                              <span class="error-message">
                                {{validation.Meassage}}
                              </span>
                            }
                          </div>
                        }
                      </div>
                    }
                  </div>
                }
              </div>
            </div>
          </div>
          <div class="modal-footer p-0">
            <div class="row">
              <div class="col-sm-12">
                <button type="submit" class="btn btn-sm btn-success team-add-btn" [disabled]="jobForm.invalid">Add</button>
              </div>
            </div>
          </div>
        </form>