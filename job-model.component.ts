import { Component, EventEmitter, Input, OnInit, Output, ViewChild, ElementRef, NgZone } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { LoaderService } from 'src/app/services/loader/loader.service';
import { SharedService } from 'src/app/services/shared/shared.service';
import { JobListConstant } from '../job-list.constant';
import moment from 'moment';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { noWhitespaceValidator } from "../utility/whitespace-validator";
import { debounceTime, distinctUntilChanged, switchMap, Subject, takeUntil } from "rxjs";
import { MatAutocompleteTrigger } from '@angular/material/autocomplete';

// Declare google maps types to avoid TypeScript errors
declare var google: any;

@Component({
    selector: 'app-job-model',
    templateUrl: './job-model.component.html',
    styleUrls: ['./job-model.component.scss'],
    standalone: false
})
export class JobModelComponent implements OnInit{
  private destroy$ = new Subject<void>();
  @Input() jobTypeList:any[] = [];
  @Input() stringBilling_State:any;
  @Input() trinity_Office_Id:any;
  @Input() type:any;
  @Input() getTeamMemberJobList: any[] = [];
  @Output() updateJobList:EventEmitter<any> =  new EventEmitter<any>()
  @ViewChild('addressInput') addressInput!: ElementRef<HTMLInputElement>;
  @ViewChild(MatAutocompleteTrigger) autocompleteTrigger!: MatAutocompleteTrigger;
  @ViewChild('googleAddress', { static: true }) googleAddress!: ElementRef;
  @ViewChild('addressContainer', { static: true }) addressContainer!: ElementRef;
 
  // Subject for address search input debouncing
  searchSubject: Subject<string> = new Subject<string>();
  searchResults: any[] = [];
  searchWord: string = '';
  jobForm = new FormGroup({});
  // Dynamic form fields configuration
  jobFormFilds:any[] = JobListConstant.job_model.jobFormFilds;
  submitted:boolean = false;

  constructor(public activeModal: NgbActiveModal,
    private loader: LoaderService,
    private sharedService: SharedService,
    private notification: NzNotificationService,
    private ngZone: NgZone){}

  /**
   * Initializes the form controls and sets up dynamic validations and options.
   * If a job type is provided, pre-selects and disables the type field.
   * Also sets up the address search observer.
   */
  ngOnInit(): void {
    this.jobFormFilds.forEach((ele: any) => {
      let validations:any[] = [];
      ele.validations.forEach((val: any) => {
        validations.push(val.type);
      });
      // Add whitespace validator for text fields
      if (ele.type === 'text') {
        validations.push(noWhitespaceValidator);
      }
      // Dynamically populate select options for job type
      if(ele.type == "select"){
       switch(ele.field_name){
        case 'type':
           ele.options = [];
           this.jobTypeList.forEach((jobType: any) => {
            ele.options.push({ text:jobType.jobType, value:jobType.jobTypeId });
           });
           break;
        default:  
       } 
      }
      this.jobForm.addControl(ele.field_name, new FormControl('',validations));
    });
    // If a job type is provided, set and disable the type field
    if(this.type){
      let type = this.jobTypeList.find((jobType:any) => jobType.jobType == this.type)
      this.jobForm.controls['type'].setValue(type.jobTypeId);
      this.jobForm.controls['type'].disable();
    } 
    this.setupSearchObserver();
    this.initGoogleAutocomplete();
  }


  /**
   * Handles form submission for saving a new job.
   * Constructs the payload and calls the save API if the form is valid.
   * Shows notifications on success or error.
   */
  save() {
    this.submitted = true;
    this.loader.show();
    if (this.jobForm.valid) {
      let payload = {
        trinity_Office_Id: this.trinity_Office_Id,
        opportunity_Id: this.jobForm.controls['opportunity_Id'].value.trim(),
        opportunity_Name: this.jobForm.controls['opportunity_Name'].value.trim(),
        start_Date: this.jobForm.controls['start_Date'].value,
        type: Number(this.jobForm.controls['type'].value),
        stringBilling_Street: this.jobForm.controls['stringBilling_Street'].value.trim(),
        stringBilling_City: this.jobForm.controls['stringBilling_City'].value.trim(),
        stringBilling_Country: "United States",
        stringBilling_Postal_Code: this.jobForm.controls['stringBilling_Postal_Code'].value,
        stringBilling_State: this.jobForm.controls['stringBilling_State'].value.trim(),
        jobType: this.type
      };
      this.sharedService.SaveJob(payload).pipe(takeUntil(this.destroy$)).subscribe((res: any) => {
        this.loader.hide();
        this.jobForm.reset();
        this.updateJobList.emit(payload);
        this.notification.success('', "Job has been successfully Added", { nzPlacement: 'bottomLeft', nzClass: 'success' });
      }, err => {
        this.loader.hide();
        this.notification.error('', err?.error?.message, { nzPlacement: 'bottomLeft', nzClass: 'error' });
      })
    }
  }

  /**
   * Triggers address search and auto-fills related fields if a result is found.
   * @param fieldName The name of the field to trigger search for (currently only 'Street Name' is supported)
   */
  searchAddress(fieldName:any){
    if(fieldName == 'Street Name') {
      this.loader.show();
      this.sharedService.addressSearch(this.jobForm.controls['stringBilling_Street'].value.trim()).subscribe((res:any) =>{
        this.loader.hide();
        if(res.data && res.data.length>0){
          // Auto-fill related address fields from the first search result
          this.jobForm.controls['opportunity_Id'].patchValue(res.data[0].opportunity_Id)
          this.jobForm.controls['stringBilling_City'].patchValue(res.data[0].billing_City)
          this.jobForm.controls['stringBilling_Postal_Code'].patchValue(res.data[0].billing_Postal_Code)
        }
      },err=>{this.loader.hide();})
    }
  }

  /**
   * Sets up the debounced address search observer for the autocomplete feature.
   * Uses RxJS operators to optimize API calls.
   */
  setupSearchObserver() {
    this.searchSubject
      .pipe(
      debounceTime(400),
      distinctUntilChanged(),
      switchMap((searchText) => {
        this.loader.show(); return this.sharedService.addressSearch(searchText.trim());
      })
      ).subscribe((res: any) => {
        this.loader.hide();
        this.searchResults = res.data || [];
      },
      (err) => {
        this.loader.hide();
      }
      );
  }

  /**
   * Handles keyup events on the street name input for address autocomplete.
   * Debounces and triggers the search if input is not empty.
   * @param inputElement The street name input element
   */
  onStreetNameKeyup(inputElement: HTMLInputElement) {
    const searchValue = inputElement.value.trim();
    if (!searchValue) {
      this.searchResults = []; // Clear results when input is empty
      return;
    }
    this.searchSubject.next(searchValue);
  }

  /**
   * Handles selection of an address option from autocomplete results.
   * Populates the form fields with the selected address data and marks all controls as touched.
   * @param formvalues The selected address object
   */
   onOptionSelected(formvalues: any): void {
    if (formvalues) {
      this.searchWord = formvalues?.billing_City;
      if (formvalues?.billing_City) this.jobForm.controls['stringBilling_City'].setValue(formvalues.billing_City);
      if (formvalues?.opportunity_Id) this.jobForm.controls['opportunity_Id'].setValue(formvalues?.opportunity_Id);
      if (formvalues?.billing_Street) this.jobForm.controls['stringBilling_Street'].setValue(formvalues?.billing_Street);
      if (formvalues?.billing_Postal_Code) this.jobForm.controls['stringBilling_Postal_Code'].setValue(formvalues?.billing_Postal_Code);
      if (formvalues?.opportunity_Name) this.jobForm.controls['opportunity_Name'].setValue(formvalues?.opportunity_Name);
      if (formvalues?.billing_State) this.jobForm.controls['stringBilling_State'].setValue(formvalues?.billing_State);
      // Mark all form controls as touched to trigger validation
      Object.keys(this.jobForm.controls).forEach(controlName => {
        const control = this.jobForm.get(controlName);
        if (control) {
          control.markAsTouched();
          control.updateValueAndValidity();
        }
      });
    }
  }

  /**
   * Checks if the job name already exists for the same job type in the team member job list.
   * Shows an error notification if a duplicate is found.
   * @returns true if the job name already exists, false otherwise.
   */
  duplicateChecking(): boolean {
    let jobNameToCheck = this.jobForm.controls['opportunity_Name'].value.trim().toLowerCase();   
    let type = this.jobTypeList.find((jobType: any) => jobType.jobTypeId == Number(this.jobForm.controls['type'].value));   
    for (const team of this.getTeamMemberJobList) {
        if (team.team_Type_Name === (this.type ?? type.jobType)) {
          for (const job of team.job) {
            if (job.job_Name.toLowerCase() === jobNameToCheck) {
              this.notification.error('', "Job name already exists with same Job type", { nzPlacement: 'bottomLeft', nzClass: 'error' });
              return true; 
            }
          }
        }   
    }
    return false; 
  }

  setManuallyEnteredAddress(event){

  }

  getAddress(event){

  }

  initGoogleAutocomplete() {
    // Add a small delay to ensure the DOM is ready
    setTimeout(() => {
      // Check if Google Maps API is loaded
      if (typeof google === 'undefined' || !google.maps || !google.maps.places) {
        console.error('Google Maps API not loaded');
        return;
      }

      // Check if PlaceAutocompleteElement is available (new API)
      if (google.maps.places.PlaceAutocompleteElement) {
        try {
          // Use the new PlaceAutocompleteElement
          const autocompleteElement = new google.maps.places.PlaceAutocompleteElement({
            componentRestrictions: { country: 'us' }, // restrict to US, remove if needed
            // types: ['geocode'], // restrict to addresses
          });

          // Replace the input element with the new autocomplete element
          const container = this.addressContainer.nativeElement;
          const originalInput = this.googleAddress.nativeElement;

          // Copy the classes and attributes from the original input
          autocompleteElement.className = originalInput.className;
          autocompleteElement.placeholder = originalInput.placeholder;

          // Replace the input with the new element
          container.replaceChild(autocompleteElement, originalInput);

          // Listen for place selection
          autocompleteElement.addEventListener('gmp-placeselect', (event: any) => {
            this.ngZone.run(() => {
              const place = event.place;
              if (!place || !place.geometry) return;

              // Extract address components
              const addressComponents: any = {};
              if (place.address_components) {
                place.address_components.forEach((component: any) => {
                  const types = component.types;
                  if (types.includes('street_number')) {
                    addressComponents.streetNumber = component.long_name;
                  }
                  if (types.includes('route')) {
                    addressComponents.streetName = component.long_name;
                  }
                  if (types.includes('locality')) {
                    addressComponents.city = component.long_name;
                  }
                  if (types.includes('administrative_area_level_1')) {
                    addressComponents.state = component.short_name;
                  }
                  if (types.includes('postal_code')) {
                    addressComponents.postalCode = component.long_name;
                  }
                  if (types.includes('country')) {
                    addressComponents.country = component.long_name;
                  }
                });
              }

              // Now patch to your form
              this.jobForm.patchValue({
                stringBilling_Street: `${addressComponents.streetNumber || ''} ${addressComponents.streetName || ''}`.trim(),
                stringBilling_City: addressComponents.city || '',
                stringBilling_State: addressComponents.state || '',
                stringBilling_Postal_Code: addressComponents.postalCode || '',
              });
            });
          });

          console.log('Google Places Autocomplete initialized with new PlaceAutocompleteElement');
        } catch (error) {
          console.error('Error initializing PlaceAutocompleteElement:', error);
          this.initLegacyAutocomplete();
        }
      } else {
        // Fallback to the old API
        this.initLegacyAutocomplete();
      }
    }, 100);
  }

  private initLegacyAutocomplete() {
    console.warn('PlaceAutocompleteElement not available, using deprecated Autocomplete API');

    try {
      const autocomplete = new google.maps.places.Autocomplete(this.googleAddress.nativeElement, {
        // types: ['geocode'], // restrict to addresses
        componentRestrictions: { country: 'us' } // restrict to US, remove if needed
      });

      autocomplete.addListener('place_changed', () => {
        this.ngZone.run(() => {
          const place = autocomplete.getPlace();
          if (!place || !place.geometry) return;

          // Extract address components
          const addressComponents: any = {};
          if (place.address_components) {
            place.address_components.forEach(component => {
              const types = component.types;
              if (types.includes('street_number')) {
                addressComponents.streetNumber = component.long_name;
              }
              if (types.includes('route')) {
                addressComponents.streetName = component.long_name;
              }
              if (types.includes('locality')) {
                addressComponents.city = component.long_name;
              }
              if (types.includes('administrative_area_level_1')) {
                addressComponents.state = component.short_name;
              }
              if (types.includes('postal_code')) {
                addressComponents.postalCode = component.long_name;
              }
              if (types.includes('country')) {
                addressComponents.country = component.long_name;
              }
            });
          }

          // Now patch to your form
          this.jobForm.patchValue({
            stringBilling_Street: `${addressComponents.streetNumber || ''} ${addressComponents.streetName || ''}`.trim(),
            stringBilling_City: addressComponents.city || '',
            stringBilling_State: addressComponents.state || '',
            stringBilling_Postal_Code: addressComponents.postalCode || '',
          });
        });
      });

      console.log('Google Places Autocomplete initialized with legacy Autocomplete API');
    } catch (error) {
      console.error('Error initializing Google Places Autocomplete:', error);
    }
  }


/**
 * Lifecycle hook that is called when the component is destroyed.
 * It completes the destroy$ subject to prevent memory leaks by notifying
 * all subscribers that the component is being destroyed and no more values will be emitted.
 */

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
